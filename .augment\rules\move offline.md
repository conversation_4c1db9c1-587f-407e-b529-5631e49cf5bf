---
type: "manual"
---

<Moving_Rules>
以下Rules适用于步进电机控制相关功能（"pysweep\move"目录下）的开发。
- 我们正在使用python调用dll格式的API，来与一个MT-22E型号的机电控制器通讯（但现在未连接）。该控制器将会控制一个真实的两轴步进电机在物理世界中进行运动。
- 存储API的dll文件位于"pysweep\move\MT_API.dll"路径下。关于它的使用方式，请你参考"参考资料\API资料"文件夹中的内容。其中”二次开发手册“最为重要，它包含了所有API函数的详细说明。”参考例程“也很有用，它包含一个简单的python环境下正确调用API的例程文件。
- 正式实验情况下，我们的PC将和电机控制器使用USB方式连接，但目前我们正在脱机情况下工作，因此无法正常连接到电机是正常的。因此，你暂时无需编写针对我们程序的任何测试。正式测试将在未来与电机连接后进行。
- 我们的步进电机只有X和Y两个运动轴，Z轴并不存在，因此你无需尝试控制Z轴。
- 部分常用参数：关于位移，函数中的1000步对应物理世界中的1mm。具体来说，我们的步进角度（StepAngle）是1.8°，驱动器细分数（Div）是20，螺距（Pitch）是4mm，传动比（LineRatio）为1。如果你需要获知控制器和电机的其他参数，可以尝试使用dll中的硬件相关API（详情请参见开发手册）。
- 我们的具体工作方式是，在"pysweep\move\controller.py"这一模块中实现所有电机控制相关的函数/类/方法/属性。
</Moving_Rules>