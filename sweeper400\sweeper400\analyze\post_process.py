"""
# 后处理模块

模块路径：`sweeper400.analyze.post_process`

本模块提供对Sweeper类采集到的原始数据进行后处理的功能。
主要包含传递函数计算等数据分析功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.axes import Axes
from typing import List, TypedDict, Tuple, Optional
from sweeper400.logger import get_logger
from sweeper400.use.sweeper import PointRawData, Point2D
from .basic_sine import extract_single_tone_information_vvi
from .my_dtypes import Waveform

# 获取模块日志器
logger = get_logger(__name__)


# 定义传递函数结果数据格式
class TransferFunctionResult(TypedDict):
    """
    传递函数计算结果格式

    ## 内部组成:
        **position**: 测量点的二维坐标
        **amp_ratio**: 幅值比（输入/输出）
        **phase_shift**: 相位差（输入-输出，弧度制）
    """

    position: Point2D
    amp_ratio: float
    phase_shift: float


def calculate_transfer_function(
    raw_data_list: List[PointRawData],
) -> List[TransferFunctionResult]:
    """
    计算Sweeper采集数据的传递函数

    对每个测量点的原始数据进行处理，计算输入输出信号的传递函数。
    具体步骤：
    1. 将每个点的所有AI波形首尾相接组成一个大波形
    2. 使用extract_single_tone_information_vvi提取AI信号的正弦波参数
    3. 提取AO信号第一个波形的正弦波参数
    4. 计算传递函数：幅值比 = AI幅值 / AO幅值，相位差 = AI相位 - AO相位

    Args:
        raw_data_list: Sweeper采集的原始数据列表，每个元素为一个测量点的数据

    Returns:
        传递函数结果列表，每个元素包含位置、幅值比和相位差信息

    Raises:
        ValueError: 当输入数据为空或格式不正确时
        IndexError: 当AO数据为空时

    Examples:
        ```python
        >>> # 假设已有采集的原始数据
        >>> raw_data = sweeper.measurement_data
        >>> tf_results = calculate_transfer_function(raw_data)
        >>> for result in tf_results:
        ...     print(f"位置: {result['position']}, 幅值比: {result['amp_ratio']:.4f}, 相位差: {result['phase_shift']:.4f}")
        ```
    """
    logger.info(f"开始计算传递函数，共 {len(raw_data_list)} 个测量点")

    # 验证输入数据
    if not raw_data_list:
        logger.error("输入数据列表为空")
        raise ValueError("输入数据列表不能为空")

    # 存储结果
    results: List[TransferFunctionResult] = []

    # 遍历每个测量点
    for point_idx, point_data in enumerate(raw_data_list):
        logger.debug(
            f"处理第 {point_idx + 1}/{len(raw_data_list)} 个点: {point_data['position']}"
        )

        try:
            # 1. 处理AI数据：将所有Waveform首尾相接
            ai_waveforms = point_data["ai_data"]
            if not ai_waveforms:
                logger.warning(f"点 {point_idx} 的AI数据为空，跳过该点")
                continue

            # 获取采样率（所有波形应该有相同的采样率）
            sampling_rate = ai_waveforms[0].sampling_rate

            # 将所有AI波形数据拼接成一个大数组
            ai_data_concatenated = np.concatenate([wf for wf in ai_waveforms])

            # 创建拼接后的大Waveform对象
            ai_combined_waveform = Waveform(
                input_array=ai_data_concatenated,
                sampling_rate=sampling_rate,
                timestamp=ai_waveforms[0].timestamp,  # 使用第一个波形的时间戳
            )

            logger.debug(
                f"AI数据拼接完成: {len(ai_waveforms)} 个波形 -> "
                f"{ai_combined_waveform.samples_num} 个采样点"
            )

            # 2. 提取AI信号的正弦波参数
            ai_sine_args = extract_single_tone_information_vvi(ai_combined_waveform)

            logger.debug(
                f"AI信号参数: 频率={ai_sine_args['frequency']:.6f}Hz, "
                f"幅值={ai_sine_args['amplitude']:.6f}, "
                f"相位={ai_sine_args['phase']:.6f}rad"
            )

            # 3. 处理AO数据：取第一个Waveform的SineArgs
            ao_waveforms = point_data["ao_data"]
            if not ao_waveforms:
                logger.warning(f"点 {point_idx} 的AO数据为空，跳过该点")
                continue

            # 获取第一个AO波形的正弦波参数
            first_ao_waveform = ao_waveforms[0]
            if first_ao_waveform.sine_args is None:
                logger.warning(
                    f"点 {point_idx} 的第一个AO波形没有sine_args属性，跳过该点"
                )
                continue

            ao_sine_args = first_ao_waveform.sine_args

            logger.debug(
                f"AO信号参数: 频率={ao_sine_args['frequency']:.6f}Hz, "
                f"幅值={ao_sine_args['amplitude']:.6f}, "
                f"相位={ao_sine_args['phase']:.6f}rad"
            )

            # 4. 计算传递函数
            # 幅值比 = AI幅值 / AO幅值
            amp_ratio = ai_sine_args["amplitude"] / ao_sine_args["amplitude"]

            # 相位差 = AI相位 - AO相位（弧度制）
            phase_shift = ai_sine_args["phase"] - ao_sine_args["phase"]

            # 将相位差归一化到 [-π, π] 区间
            phase_shift = np.arctan2(np.sin(phase_shift), np.cos(phase_shift))

            logger.debug(
                f"传递函数计算结果: 幅值比={amp_ratio:.6f}, 相位差={phase_shift:.6f}rad"
            )

            # 5. 存储结果
            result: TransferFunctionResult = {
                "position": point_data["position"],
                "amp_ratio": float(amp_ratio),
                "phase_shift": float(phase_shift),
            }
            results.append(result)

            logger.info(
                f"点 {point_idx + 1} 处理完成: 位置={result['position']}, "
                f"幅值比={result['amp_ratio']:.6f}, 相位差={result['phase_shift']:.6f}rad"
            )

        except Exception as e:
            logger.error(f"处理点 {point_idx} 时发生错误: {e}", exc_info=True)
            # 继续处理下一个点
            continue

    logger.info(f"传递函数计算完成，成功处理 {len(results)}/{len(raw_data_list)} 个点")

    return results


def plot_transfer_function_spatial_distribution(
    raw_data_list: List[PointRawData],
    figsize: Tuple[float, float] = (14, 6),
    amp_cmap: str = "viridis",
    phase_cmap: str = "twilight",
    save_path: Optional[str] = None,
) -> Tuple[Figure, Tuple[Axes, Axes]]:
    """
    绘制传递函数的空间分布图

    该函数首先调用calculate_transfer_function计算传递函数，然后绘制幅值比和相位差的
    二维空间分布图。两张子图并排显示，使用颜色表示幅值比/相位差的大小。

    Args:
        raw_data_list: Sweeper采集的原始数据列表
        figsize: 图形尺寸 (宽, 高)，单位为英寸，默认为 (14, 6)
        amp_cmap: 幅值比图的colormap名称，默认为 "viridis"
        phase_cmap: 相位差图的colormap名称，默认为 "twilight"
        save_path: 保存图片的路径，如果为None则不保存，默认为None

    Returns:
        fig: matplotlib Figure对象
        (ax1, ax2): 包含两个Axes对象的元组，分别对应幅值比图和相位差图

    Raises:
        ValueError: 当输入数据为空或计算结果为空时

    Examples:
        ```python
        >>> # 假设已有采集的原始数据
        >>> raw_data = sweeper.measurement_data
        >>> fig, (ax1, ax2) = plot_transfer_function_spatial_distribution(raw_data)
        >>> plt.show()
        >>> # 或者保存图片
        >>> fig, axes = plot_transfer_function_spatial_distribution(
        ...     raw_data, save_path="transfer_function.png"
        ... )
        ```
    """
    logger.info("开始绘制传递函数空间分布图")

    # 1. 计算传递函数
    tf_results = calculate_transfer_function(raw_data_list)

    if not tf_results:
        logger.error("传递函数计算结果为空，无法绘图")
        raise ValueError("传递函数计算结果为空")

    logger.info(f"成功计算 {len(tf_results)} 个点的传递函数")

    # 2. 提取数据
    x_coords = np.array([result["position"].x for result in tf_results])
    y_coords = np.array([result["position"].y for result in tf_results])
    amp_ratios = np.array([result["amp_ratio"] for result in tf_results])
    phase_shifts = np.array([result["phase_shift"] for result in tf_results])

    logger.debug(
        f"数据范围: X=[{x_coords.min():.2f}, {x_coords.max():.2f}], "
        f"Y=[{y_coords.min():.2f}, {y_coords.max():.2f}], "
        f"幅值比=[{amp_ratios.min():.4f}, {amp_ratios.max():.4f}], "
        f"相位差=[{phase_shifts.min():.4f}, {phase_shifts.max():.4f}]"
    )

    # 3. 创建图形和子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

    # 4. 绘制幅值比分布图
    scatter1 = ax1.scatter(
        x_coords,
        y_coords,
        c=amp_ratios,
        cmap=amp_cmap,
        s=100,
        edgecolors="black",
        linewidths=0.5,
    )
    ax1.set_xlabel("X 坐标 (mm)", fontsize=12)
    ax1.set_ylabel("Y 坐标 (mm)", fontsize=12)
    ax1.set_title("传递函数 - 幅值比空间分布", fontsize=14, fontweight="bold")
    ax1.grid(True, alpha=0.3, linestyle="--")
    ax1.set_aspect("equal", adjustable="box")

    # 添加颜色条
    cbar1 = fig.colorbar(scatter1, ax=ax1, label="幅值比")
    cbar1.ax.tick_params(labelsize=10)

    logger.debug("幅值比分布图绘制完成")

    # 5. 绘制相位差分布图
    scatter2 = ax2.scatter(
        x_coords,
        y_coords,
        c=phase_shifts,
        cmap=phase_cmap,
        s=100,
        edgecolors="black",
        linewidths=0.5,
    )
    ax2.set_xlabel("X 坐标 (mm)", fontsize=12)
    ax2.set_ylabel("Y 坐标 (mm)", fontsize=12)
    ax2.set_title("传递函数 - 相位差空间分布", fontsize=14, fontweight="bold")
    ax2.grid(True, alpha=0.3, linestyle="--")
    ax2.set_aspect("equal", adjustable="box")

    # 添加颜色条
    cbar2 = fig.colorbar(scatter2, ax=ax2, label="相位差 (rad)")
    cbar2.ax.tick_params(labelsize=10)

    logger.debug("相位差分布图绘制完成")

    # 6. 调整布局
    plt.tight_layout()

    # 7. 保存图片（如果指定了路径）
    if save_path is not None:
        fig.savefig(save_path, dpi=300, bbox_inches="tight")
        logger.info(f"图片已保存至: {save_path}")

    logger.info("传递函数空间分布图绘制完成")

    return fig, (ax1, ax2)
