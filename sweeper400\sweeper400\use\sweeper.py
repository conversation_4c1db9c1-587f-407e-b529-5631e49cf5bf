"""
声学扫场测量模块

该模块提供声学扫场测量的核心功能，协同控制步进电机和数据采集系统，
实现空间中多点位的自动化声场信号采集。

主要功能：
1. 点阵管理：定义和管理二维空间中的测量点位
2. 扫场控制：自动控制机械臂移动到各个点位
3. 数据采集：在每个点位采集指定数量的声场信号chunk
4. 数据存储：规范化存储采集的数据，便于后续处理

核心类：
- Point2D: 二维空间点坐标的命名元组
- AcousticSweeper: 声学扫场测量控制器

使用示例：
    # 定义点阵
    grid = [
        Point2D(10.0, 20.0),
        Point2D(15.0, 20.0),
        Point2D(20.0, 20.0),
    ]

    # 创建扫场测量器
    sweeper = AcousticSweeper(
        move_controller=motor_ctrl,
        measure_controller=measure_controller,
        point_list=grid,
        chunks_per_point=6
    )

    # 执行扫场测量
    sweeper.sweep()

    # 保存数据
    sweeper.save_data("measurement_data.pkl")

注意事项：
- 使用前建议对步进电机进行零位校准
- 点阵中的坐标单位为毫米（mm）
- 采集过程中会自动等待电机停稳后再开始数据采集
"""

import time
import pickle
import threading
import numpy as np
from pathlib import Path
from typing import List, NamedTuple, TypedDict

from sweeper400.logger import get_logger
from sweeper400.measure import HiPerfCSSIO
from sweeper400.move import MotorController
from sweeper400.analyze import PositiveInt, PositiveFloat, Waveform

# 获取模块专用日志器
logger = get_logger(__name__)


# 定义二维空间点坐标的命名元组
class Point2D(NamedTuple):
    """
    二维空间点坐标

    Attributes:
        x: X轴坐标（mm）
        y: Y轴坐标（mm）
    """

    x: float
    y: float


# 辅助函数：以常见模式获取点阵（grid/point_list）
# 1. 矩形网格
def get_square_grid(
    x_start: float,
    x_end: float,
    x_points: PositiveInt,  # 可为1
    y_start: float,
    y_end: float,
    y_points: PositiveInt,  # 可为1
) -> List[Point2D]:
    """
    生成矩形网格点阵

    在指定的矩形区域内生成均匀分布的网格点阵。
    点的顺序为：先沿点数较少的轴扫描（相等则X轴优先）。

    Args:
        x_start: X轴起始坐标（mm）
        x_end: X轴结束坐标（mm）
        x_points: X轴方向的点数，可为1
        y_start: Y轴起始坐标（mm）
        y_end: Y轴结束坐标（mm）
        y_points: Y轴方向的点数，可为1

    Returns:
        List[Point2D]: 生成的点阵列表

    Raises:
        ValueError: 当参数无效时

    Examples:
        >>> # 生成3x3的网格，X轴从0到20mm，Y轴从0到20mm
        >>> grid = generate_grid_pattern(0, 20, 3, 0, 20, 3)
        >>> len(grid)
        9
    """
    if x_start >= x_end or y_start >= y_end:
        raise ValueError("起始坐标必须小于结束坐标")

    logger.info(
        f"生成网格点阵: X[{x_start}, {x_end}]mm x {x_points}点, "
        f"Y[{y_start}, {y_end}]mm x {y_points}点"
    )

    # 生成X和Y坐标数组
    x_coords = np.linspace(x_start, x_end, x_points)
    y_coords = np.linspace(y_start, y_end, y_points)

    # 生成网格点阵（点数较少的轴优先）
    grid: List[Point2D] = []
    if x_points <= y_points:
        for y in y_coords:
            for x in x_coords:
                grid.append(Point2D(float(x), float(y)))
    else:
        for x in x_coords:
            for y in y_coords:
                grid.append(Point2D(float(x), float(y)))

    logger.info(f"网格点阵生成完成，共 {len(grid)} 个点")

    return grid


# 2. 直线
def get_line_grid(
    x_start: float,
    y_start: float,
    x_end: float,
    y_end: float,
    num_points: PositiveInt,
) -> List[Point2D]:
    """
    生成直线点阵

    在两点之间生成均匀分布的直线点阵。

    Args:
        x_start: 起始点X坐标（mm）
        y_start: 起始点Y坐标（mm）
        x_end: 结束点X坐标（mm）
        y_end: 结束点Y坐标（mm）
        num_points: 点数

    Returns:
        List[Point2D]: 生成的点阵列表

    Raises:
        ValueError: 当参数无效时

    Examples:
        >>> # 生成从(0,0)到(100,0)的5个点
        >>> line = generate_line_pattern(0, 0, 100, 0, 5)
        >>> len(line)
        5
    """

    logger.info(
        f"生成直线点阵: 从({x_start}, {y_start})到({x_end}, {y_end}), {num_points}个点"
    )

    # 生成坐标数组
    x_coords = np.linspace(x_start, x_end, num_points)
    y_coords = np.linspace(y_start, y_end, num_points)

    # 生成点阵
    line: List[Point2D] = []
    for x, y in zip(x_coords, y_coords):
        line.append(Point2D(float(x), float(y)))

    logger.info(f"直线点阵生成完成，共 {len(line)} 个点")

    return line


# 定义Sweeper采集的单点原始数据格式
class PointRawData(TypedDict):
    """
    Sweeper采集的单点原始数据格式

    ## 内部组成:
        **position**: 测量点的二维坐标
        **ai_data**: 该点采集的所有AI波形
        **ao_data**: 该点对应的所有AO波形
    """

    position: Point2D
    ai_data: List[Waveform]
    ao_data: List[Waveform]


def load_measurement_data(file_path: str | Path) -> List[PointRawData]:
    """
    从文件加载测量数据

    加载由AcousticSweeper.save_data()保存的测量数据。

    Args:
        file_path: 数据文件的路径（.pkl文件）

    Returns:
        List[PointRawData]: 测量数据列表，每个元素包含：
            - "position": Point2D对象，表示该点的坐标
            - "ai_data": List[Waveform]，该点采集的所有AI波形
            - "ao_data": List[Waveform]，该点对应的所有AO波形

    Raises:
        FileNotFoundError: 当文件不存在时
        IOError: 当文件读取失败时

    Examples:
        >>> data = load_measurement_data("measurement_data.pkl")
        >>> print(f"加载了 {len(data)} 个点的数据")
        >>> first_point_position = data[0]["position"]
        >>> print(f"第一个点的坐标: ({first_point_position.x}, {first_point_position.y})")
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"数据文件不存在: {file_path}")

    logger.info(f"开始加载测量数据: {file_path}")

    try:
        with open(file_path, "rb") as f:
            measurement_data = pickle.load(f)

        logger.info(f"数据加载成功，共 {len(measurement_data)} 个点")
        return measurement_data

    except Exception as e:
        logger.error(f"数据加载失败: {e}", exc_info=True)
        raise IOError(f"无法从 {file_path} 加载数据: {e}")


class Sweeper:
    """
    声学扫场测量控制器

    该类协同控制步进电机和数据采集系统，实现自动化的声学扫场测量。
    在预定义的点阵中，依次移动到每个点位，采集指定数量的声场信号chunk。

    主要功能：
    1. 点阵管理：存储和验证测量点阵
    2. 运动控制：自动控制机械臂移动到各个点位
    3. 数据采集：在每个点位采集指定数量的chunk
    4. 数据存储：规范化存储所有点位的测量数据
    5. 状态监控：实时监控测量进度和状态

    Attributes:
        move_controller: 步进电机控制器实例
        measure_controller: 高性能连续同步IO实例
        point_list: 测量点阵列表
        chunks_per_point: 每个点采集的chunk数量
        settle_time: 电机停止后的稳定等待时间（秒）
    """

    def __init__(
        self,
        move_controller: MotorController,
        measure_controller: HiPerfCSSIO,
        point_list: List[Point2D],
        chunks_per_point: PositiveInt = 5,
        settle_time: PositiveFloat = 0.5,
    ) -> None:
        """
        初始化声学扫场测量控制器

        Args:
            move_controller: 步进电机控制器实例（需已完成初始化）
            measure_controller: 高性能连续同步IO实例（需已配置好输出波形和导出函数）
            point_list: 测量点阵，Point2D对象的列表
            chunks_per_point: 每个点采集的chunk数量，默认5
            settle_time: 电机停止后的稳定等待时间（秒），默认0.5秒

        Raises:
            ValueError: 当参数无效时
        """
        logger.info("初始化 AcousticSweeper")

        # 验证参数
        if not point_list:
            raise ValueError("测量点阵不能为空")

        # 存储核心组件
        self._move_controller = move_controller
        self._measure_controller = measure_controller

        # 存储测量参数
        self._point_list = point_list
        self._chunks_per_point = chunks_per_point
        self._settle_time = settle_time

        # 数据存储
        self._measurement_data: List[PointRawData] = []
        # 数据结构（列表索引即为点序号）:
        # [
        #     {
        #         "position": Point2D,
        #         "ai_data": List[Waveform],
        #         "ao_data": List[Waveform]
        #     },
        #     ...
        # ]

        # 状态标志
        self._is_running = False  # 该标志变化不频繁，暂不使用锁保护
        self._current_point_index = 0
        self._point_index_lock = threading.Lock()  # 保护 _current_point_index 的线程锁
        self._enough_chunks_event = threading.Event()

        # 替换原有的导出函数
        self._original_export_function = self._measure_controller.export_function
        self._measure_controller.export_function = self._data_export_callback

        logger.info(
            f"AcousticSweeper 初始化完成 - "
            f"测量点数: {len(self._point_list)}, "
            f"每点chunk数: {self._chunks_per_point}, "
            f"稳定时间: {self._settle_time}s"
        )
        logger.debug(f"测量点阵: {self._point_list}")

    def _data_export_callback(
        self, ai_waveform: Waveform, ao_waveform: Waveform, chunks_num: int
    ) -> None:
        """
        数据导出回调函数

        该函数会被HiPerfCSSIO在（后台工作线程中）每次数据导出时调用，用于收集测量数据。
        使用线程锁保证线程安全。

        Args:
            ai_waveform: 采集到的AI波形
            ao_waveform: 对应的AO波形
            chunks_num: 当前chunk编号（从1开始）
        """
        if not self._is_running:
            return

        # 使用线程锁安全地获取当前点的索引
        with self._point_index_lock:
            point_idx = self._current_point_index

        # 初始化当前点的数据存储（如果尚未初始化）
        # 由于点是按顺序处理的，point_idx 应该等于当前列表长度
        if point_idx >= len(self._measurement_data):
            current_position = self._point_list[point_idx]
            self._measurement_data.append(
                {
                    "position": current_position,
                    "ai_data": [],
                    "ao_data": [],
                }
            )
            logger.debug(f"初始化点 {point_idx} 的数据存储，位置: {current_position}")

        # 存储数据
        self._measurement_data[point_idx]["ai_data"].append(ai_waveform)
        self._measurement_data[point_idx]["ao_data"].append(ao_waveform)

        # 检查是否已采集足够chunk
        if chunks_num >= self._chunks_per_point:
            self._enough_chunks_event.set()

        logger.debug(
            f"点 {point_idx} 采集第 {chunks_num}/{self._chunks_per_point} 个chunk"
        )

    def _move_to_point(self, point: Point2D) -> bool:
        """
        移动到指定点位

        Args:
            point: 目标点位坐标

        Returns:
            bool: 移动是否成功
        """
        logger.info(f"移动到点位: ({point.x:.3f}, {point.y:.3f}) mm")

        # 执行2D绝对运动
        success = self._move_controller.move_absolute_2D(x_mm=point.x, y_mm=point.y)

        if not success:
            logger.error(f"移动到点位 ({point.x:.3f}, {point.y:.3f}) 失败")
            return False

        # 等待电机稳定
        logger.debug(f"等待电机稳定 {self._settle_time}s")
        time.sleep(self._settle_time)

        return True

    def _collect_data_at_point(self, point_index: int) -> bool:
        """
        在当前点位采集数据。
        阻塞操作，会等待_enough_chunks_event事件到达。

        Args:
            point_index: 点位索引

        Returns:
            bool: 采集是否成功
        """
        point = self._point_list[point_index]
        logger.info(
            f"开始在点 {point_index} ({point.x:.3f}, {point.y:.3f}) 采集数据，"
            f"目标chunk数: {self._chunks_per_point}"
        )

        # 预估持续时间并设置超时
        chunk_duration = self._measure_controller._output_waveform.duration  # type: ignore
        expected_duration = chunk_duration * self._chunks_per_point
        timeout = expected_duration * 2 + 5.0  # 设置超时为预期时间的2倍+5秒

        # 启用数据导出
        self._measure_controller.enable_export = True
        logger.debug("已启用数据导出")

        try:
            # 等待数据就绪事件或超时
            chunks_ready = self._enough_chunks_event.wait(timeout=timeout)

            # 事件成功到达
            if chunks_ready:
                # 停止数据导出
                self._measure_controller.enable_export = False
                # 清除事件标志
                self._enough_chunks_event.clear()
                logger.info(f"点 {point_index} 数据采集完成")
                return True

            # 事件超时
            else:
                self._measure_controller.enable_export = False
                logger.error(
                    f"点 {point_index} 数据采集超时（{timeout:.1f}s），强制停止"
                )
                return False

        except Exception as e:
            self._measure_controller.enable_export = False
            logger.error(f"点 {point_index} 数据采集过程中发生异常: {e}")
            return False

    def sweep(self) -> bool:
        """
        执行完整的扫场测量

        依次移动到点阵中的每个点位，并在每个点位采集指定数量的数据。

        Returns:
            bool: 扫场测量是否成功完成
        """
        logger.info("=" * 60)
        logger.info("开始扫场测量")
        logger.info(f"总测量点数: {len(self._point_list)}")
        logger.info(f"每点chunk数: {self._chunks_per_point}")
        logger.info("=" * 60)

        # 设置运行标志
        self._is_running = True

        # 记录开始时间
        sweep_start_time = time.time()

        try:
            # 遍历所有测量点
            for point_index, point in enumerate(self._point_list):
                logger.info(
                    f"\n--- 处理点 {point_index + 1}/{len(self._point_list)} ---"
                )

                # 使用线程锁安全地更新当前点索引
                with self._point_index_lock:
                    self._current_point_index = point_index

                # 移动到目标点位
                if not self._move_to_point(point):
                    logger.error(f"移动到点 {point_index} 失败，终止扫场测量")
                    return False

                # 在当前点位采集数据
                if not self._collect_data_at_point(point_index):
                    logger.error(f"点 {point_index} 数据采集失败，终止扫场测量")
                    return False

            # 计算总耗时
            total_time = time.time() - sweep_start_time

            logger.info("=" * 60)
            logger.info("扫场测量完成！")
            logger.info(f"总测量点数: {len(self._point_list)}")
            logger.info(f"总耗时: {total_time:.2f}s ({total_time / 60:.2f}min)")
            logger.info(f"平均每点耗时: {total_time / len(self._point_list):.2f}s")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"扫场测量过程中发生异常: {e}", exc_info=True)
            return False

        finally:
            # 清理运行标志
            self._is_running = False

    def get_data(self) -> List[PointRawData]:
        """
        获取测量数据

        Returns:
            List[PointRawData]: 测量数据列表，每个元素包含一个点的数据
        """
        return self._measurement_data

    def save_data(
        self,
        file_path: str | Path,
    ) -> None:
        """
        保存测量数据到文件

        使用pickle直接保存原始的_measurement_data列表，保证数据组织形式在整个生命周期中的一致性。

        数据结构：
        List[PointRawData]，每个PointRawData包含：
        - "position": Point2D对象，表示该点的坐标
        - "ai_data": List[Waveform]，该点采集的所有AI波形
        - "ao_data": List[Waveform]，该点对应的所有AO波形

        Args:
            file_path: 保存文件的路径（建议使用.pkl扩展名）

        Raises:
            ValueError: 当没有数据可保存时
            IOError: 当文件保存失败时
        """
        # python中，空容器视为False
        if not self._measurement_data:
            raise ValueError("没有可保存的数据，请先执行扫场测量")

        logger.info(f"开始保存测量数据到: {file_path}")

        # 转换为Path对象
        file_path = Path(file_path)

        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # 使用pickle保存原始数据
        try:
            with open(file_path, "wb") as f:
                pickle.dump(self._measurement_data, f, protocol=pickle.HIGHEST_PROTOCOL)

            logger.info(f"数据保存成功: {file_path}")
            logger.info(f"文件大小: {file_path.stat().st_size / 1024 / 1024:.2f} MB")
            logger.info(f"保存了 {len(self._measurement_data)} 个点的数据")
        except Exception as e:
            logger.error(f"数据保存失败: {e}", exc_info=True)
            raise IOError(f"无法保存数据到 {file_path}: {e}")

    def reset(self) -> None:
        """
        重置测量器状态

        清除所有已采集的数据，重置状态标志，准备进行新的测量。
        """
        logger.info("重置 AcousticSweeper 状态")

        # 清除数据
        self._measurement_data.clear()

        # 重置状态标志（使用线程锁保护）
        self._is_running = False
        with self._point_index_lock:
            self._current_point_index = 0
        self._enough_chunks_event.clear()

        logger.info("AcousticSweeper 状态已重置")

    def cleanup(self) -> None:
        """
        清理资源

        恢复原有的导出函数，清理状态。
        """
        logger.info("清理 AcousticSweeper 资源")

        # 恢复原有的导出函数
        self._measure_controller.export_function = self._original_export_function

        # 重置状态
        self.reset()

        logger.info("AcousticSweeper 资源清理完成")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.cleanup()
